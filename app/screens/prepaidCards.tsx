import React, { useState, useEffect } from 'react';
import { View, Text, TouchableOpacity, TextInput, StyleSheet, ScrollView, Alert, Modal } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { router } from 'expo-router';
import i18n from '../i18n';
import { useSettings } from '../context/SettingsContext';
import { getPrepaidCards, addPrepaidCard, updatePrepaidCard, deletePrepaidCard } from '../constants/Storage';

interface PrepaidCard {
    id: number;
    name: string;
    balance: number;
    recharge_date: string;
    created_at: string;
}

const PrepaidCards = () => {
    const [cards, setCards] = useState<PrepaidCard[]>([]);
    const [showAddModal, setShowAddModal] = useState(false);
    const [editingCard, setEditingCard] = useState<PrepaidCard | null>(null);
    const [cardName, setCardName] = useState('');
    const [cardBalance, setCardBalance] = useState('');
    const [rechargeDate, setRechargeDate] = useState(new Date().toISOString().split('T')[0]);
    const { currency } = useSettings();

    useEffect(() => {
        loadCards();
    }, []);

    const loadCards = async () => {
        try {
            const cardsData = await getPrepaidCards();
            setCards(cardsData);
        } catch (error) {
            console.error('Failed to load prepaid cards:', error);
        }
    };

    const handleAddCard = async () => {
        if (!cardName.trim()) {
            Alert.alert(i18n.t('common.error'), i18n.t('prepaidCards.cardNameRequired'));
            return;
        }

        const balance = parseFloat(cardBalance);
        if (isNaN(balance) || balance < 0) {
            Alert.alert(i18n.t('common.error'), i18n.t('prepaidCards.invalidBalance'));
            return;
        }

        try {
            if (editingCard) {
                // 更新现有卡片
                await updatePrepaidCard(editingCard.id, {
                    name: cardName.trim(),
                    balance: balance,
                    recharge_date: rechargeDate || new Date().toISOString(),
                });
            } else {
                // 添加新卡片
                await addPrepaidCard({
                    name: cardName.trim(),
                    balance: balance,
                    recharge_date: rechargeDate || new Date().toISOString(),
                });
            }

            resetForm();
            setShowAddModal(false);
            loadCards(); // 重新加载数据
        } catch (error) {
            console.error('Failed to save card:', error);
            Alert.alert(i18n.t('common.error'), i18n.t('prepaidCards.addCardFailed'));
        }
    };

    const handleEditCard = (card: PrepaidCard) => {
        setEditingCard(card);
        setCardName(card.name);
        setCardBalance(card.balance.toString());
        setRechargeDate(card.recharge_date);
        setShowAddModal(true);
    };

    const handleDeleteCard = async (cardId: number) => {

        Alert.alert(
            i18n.t('prepaidCards.confirmDelete'),
            i18n.t('prepaidCards.confirmDeleteMessage'),
            [
                { text: i18n.t('common.cancel'), style: 'cancel' },
                {
                    text: i18n.t('common.delete'),
                    style: 'destructive',
                    onPress: async () => {
                        try {
                            await deletePrepaidCard(cardId);
                            loadCards(); // 重新加载数据
                        } catch (error) {
                            console.error('Failed to delete card:', error);
                            Alert.alert(i18n.t('common.error'), i18n.t('prepaidCards.deleteCardFailed'));
                        }
                    }
                }
            ]
        );
    };

    const resetForm = () => {
        setCardName('');
        setCardBalance('');
        setRechargeDate(new Date().toISOString().split('T')[0]);
        setEditingCard(null);
    };

    const renderCard = (card: PrepaidCard) => (
        <View key={card.id} style={styles.cardItem}>
            <View style={styles.cardHeader}>
                <View style={styles.cardIcon}>
                    <Ionicons name="card" size={24} color="#E91E63" />
                </View>
                <View style={styles.cardInfo}>
                    <Text style={styles.cardName}>{card.name}</Text>
                    <Text style={styles.cardBalance}>
                        余额: {currency}{card.balance.toFixed(2)}
                    </Text>
                    <Text style={styles.cardDate}>
                        充值日期: {card.recharge_date}
                    </Text>
                </View>
                <View style={styles.cardActions}>
                    <TouchableOpacity
                        style={styles.actionButton}
                        onPress={() => handleEditCard(card)}
                    >
                        <Ionicons name="pencil-outline" size={20} color="#666" />
                    </TouchableOpacity>
                    <TouchableOpacity
                        style={styles.actionButton}
                        onPress={() => handleDeleteCard(card.id)}
                    >
                        <Ionicons name="trash-outline" size={20} color="#dc4446" />
                    </TouchableOpacity>
                </View>
            </View>
        </View>
    );

    const renderAddModal = () => (
        <Modal
            visible={showAddModal}
            transparent={true}
            animationType="slide"
            onRequestClose={() => {
                resetForm();
                setShowAddModal(false);
            }}
        >
            <View style={styles.modalOverlay}>
                <View style={styles.modalContent}>
                    <View style={styles.modalHeader}>
                        <Text style={styles.modalTitle}>
                            {editingCard ? i18n.t('prepaidCards.editCard') : i18n.t('prepaidCards.addCard')}
                        </Text>
                        <TouchableOpacity
                            onPress={() => {
                                resetForm();
                                setShowAddModal(false);
                            }}
                            style={styles.modalCloseButton}
                        >
                            <Ionicons name="close" size={24} color="#666" />
                        </TouchableOpacity>
                    </View>

                    <View style={styles.modalBody}>
                        <Text style={styles.inputLabel}>{i18n.t('prepaidCards.cardName')}</Text>
                        <TextInput
                            style={styles.textInput}
                            placeholder={i18n.t('prepaidCards.cardNameRequired')}
                            value={cardName}
                            onChangeText={setCardName}
                        />

                        <Text style={styles.inputLabel}>{i18n.t('prepaidCards.cardBalance')}</Text>
                        <TextInput
                            style={styles.textInput}
                            placeholder={i18n.t('prepaidCards.invalidBalance')}
                            value={cardBalance}
                            onChangeText={setCardBalance}
                            keyboardType="decimal-pad"
                        />

                        <Text style={styles.inputLabel}>{i18n.t('prepaidCards.rechargeDate')}</Text>
                        <TextInput
                            style={styles.textInput}
                            placeholder="YYYY-MM-DD"
                            value={rechargeDate}
                            onChangeText={setRechargeDate}
                        />
                    </View>

                    <View style={styles.modalActions}>
                        <TouchableOpacity
                            style={styles.cancelButton}
                            onPress={() => {
                                resetForm();
                                setShowAddModal(false);
                            }}
                        >
                            <Text style={styles.cancelButtonText}>{i18n.t('common.cancel')}</Text>
                        </TouchableOpacity>
                        <TouchableOpacity
                            style={styles.confirmButton}
                            onPress={handleAddCard}
                        >
                            <Text style={styles.confirmButtonText}>
                                {editingCard ? i18n.t('common.edit') : i18n.t('common.add')}
                            </Text>
                        </TouchableOpacity>
                    </View>
                </View>
            </View>
        </Modal>
    );

    return (
        <View style={styles.container}>
            <ScrollView style={styles.scrollView}>
                <TouchableOpacity
                    style={styles.addButton}
                    onPress={() => {
                        setShowAddModal(true);
                    }}
                >
                    <Ionicons name="add" size={24} color="white" />
                    <Text style={styles.addButtonText}>{i18n.t('prepaidCards.addCard')}</Text>
                </TouchableOpacity>

                {cards.length === 0 ? (
                    <View style={styles.emptyState}>
                        <Ionicons name="card-outline" size={64} color="#ccc" />
                        <Text style={styles.emptyText}>{i18n.t('prepaidCards.noCards')}</Text>
                        <Text style={styles.emptySubtext}>{i18n.t('prepaidCards.addFirstCard')}</Text>
                    </View>
                ) : (
                    <View style={styles.cardsList}>
                        {cards.map(renderCard)}
                    </View>
                )}
            </ScrollView>

            {renderAddModal()}
        </View>
    );
};

const styles = StyleSheet.create({
    container: {
        flex: 1,
        backgroundColor: '#f5f5f5',
    },
    scrollView: {
        flex: 1,
        padding: 16,
    },
    addButton: {
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'center',
        backgroundColor: '#E91E63',
        paddingVertical: 12,
        paddingHorizontal: 20,
        borderRadius: 12,
        marginBottom: 20,
        position: 'relative',
    },
    addButtonText: {
        color: 'white',
        fontSize: 16,
        fontWeight: '500',
        marginLeft: 8,
    },
    emptyState: {
        alignItems: 'center',
        justifyContent: 'center',
        paddingVertical: 60,
    },
    emptyText: {
        fontSize: 18,
        color: '#666',
        marginTop: 16,
        fontWeight: '500',
    },
    emptySubtext: {
        fontSize: 14,
        color: '#999',
        marginTop: 8,
        textAlign: 'center',
    },
    cardsList: {
        gap: 12,
    },
    cardItem: {
        backgroundColor: 'white',
        borderRadius: 12,
        padding: 16,
        shadowColor: '#000',
        shadowOffset: {
            width: 0,
            height: 2,
        },
        shadowOpacity: 0.1,
        shadowRadius: 3.84,
        elevation: 5,
    },
    cardHeader: {
        flexDirection: 'row',
        alignItems: 'center',
    },
    cardIcon: {
        width: 48,
        height: 48,
        borderRadius: 24,
        backgroundColor: '#fce4ec',
        alignItems: 'center',
        justifyContent: 'center',
        marginRight: 12,
    },
    cardInfo: {
        flex: 1,
    },
    cardName: {
        fontSize: 16,
        fontWeight: '600',
        color: '#333',
        marginBottom: 4,
    },
    cardBalance: {
        fontSize: 14,
        color: '#E91E63',
        fontWeight: '500',
        marginBottom: 2,
    },
    cardDate: {
        fontSize: 12,
        color: '#666',
    },
    cardActions: {
        flexDirection: 'row',
        gap: 8,
    },
    actionButton: {
        padding: 8,
        borderRadius: 8,
        backgroundColor: '#f5f5f5',
    },
    // Modal styles
    modalOverlay: {
        flex: 1,
        backgroundColor: 'rgba(0, 0, 0, 0.5)',
        justifyContent: 'center',
        alignItems: 'center',
    },
    modalContent: {
        backgroundColor: 'white',
        borderRadius: 16,
        width: '90%',
        maxWidth: 400,
    },
    modalHeader: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
        padding: 20,
        borderBottomWidth: 1,
        borderBottomColor: '#f0f0f0',
    },
    modalTitle: {
        fontSize: 18,
        fontWeight: '600',
        color: '#333',
    },
    modalCloseButton: {
        padding: 4,
    },
    modalBody: {
        padding: 20,
    },
    inputLabel: {
        fontSize: 14,
        fontWeight: '500',
        color: '#333',
        marginBottom: 8,
        marginTop: 12,
    },
    textInput: {
        borderWidth: 1,
        borderColor: '#ddd',
        borderRadius: 8,
        padding: 12,
        fontSize: 16,
        backgroundColor: '#f9f9f9',
    },
    modalActions: {
        flexDirection: 'row',
        padding: 20,
        gap: 12,
    },
    cancelButton: {
        flex: 1,
        paddingVertical: 12,
        borderRadius: 8,
        backgroundColor: '#f5f5f5',
        alignItems: 'center',
    },
    cancelButtonText: {
        fontSize: 16,
        color: '#666',
    },
    confirmButton: {
        flex: 1,
        paddingVertical: 12,
        borderRadius: 8,
        backgroundColor: '#E91E63',
        alignItems: 'center',
    },
    confirmButtonText: {
        fontSize: 16,
        color: 'white',
        fontWeight: '500',
    },
    premiumBadge: {
        position: 'absolute',
        top: -8,
        right: -8,
        backgroundColor: '#FF9800',
        paddingHorizontal: 6,
        paddingVertical: 2,
        borderRadius: 8,
    },
    premiumBadgeText: {
        fontSize: 10,
        color: 'white',
        fontWeight: '600',
    },
});

export default PrepaidCards;
