import { useEffect } from 'react';
import { Stack } from "expo-router";
import { initDatabase } from './constants/Storage';
import { GestureHandlerRootView } from 'react-native-gesture-handler';
import { TransactionProvider } from './context/TransactionContext';
import { CategoryProvider } from './context/CategoryContext';
import './i18n'; // 导入 i18n 配置
import i18n from './i18n'; // 导入 i18n 配置
import { SettingsProvider } from './context/SettingsContext';
import { ThemeProvider, useTheme } from './context/ThemeContext';
import { crashReporter } from './utils/crashReporting';
import { ErrorBoundary } from './components/ErrorBoundary';

function AppStack() {
  const { theme } = useTheme();

  return (
    <SettingsProvider>
      <TransactionProvider>
        <CategoryProvider>
          <Stack
            screenOptions={{
              headerStyle: {
                backgroundColor: theme.surface,
              },
              headerTintColor: theme.textSecondary,
              headerTitleStyle: {
                fontWeight: 'bold',
                color: theme.text,
              },
            }}>
            <Stack.Screen name="index" options={{ title: i18n.t('home.title') }} />
            <Stack.Screen name="screens/add" options={{ title: i18n.t('add.title') }} />
            <Stack.Screen
              name="screens/categories"
              options={{
                title: i18n.t('categories.title'),
                headerTintColor: theme.textSecondary,
              }}
            />
            <Stack.Screen
              name="screens/profile"
              options={{
                title: i18n.t('profile.profile'),
                headerTintColor: theme.textSecondary,
              }}
            />
            <Stack.Screen
              name="screens/budget"
              options={{
                title: i18n.t('profile.memberBudget'),
                headerTintColor: theme.textSecondary,
              }}
            />
            <Stack.Screen
              name="screens/settings"
              options={{
                title: i18n.t('settings.title'),
                headerTintColor: theme.textSecondary,
              }}
            />
            <Stack.Screen
              name="screens/statsDetail"
              options={{
                title: i18n.t('stats.detail'),
                headerTintColor: theme.textSecondary,
              }}
            />
            <Stack.Screen
              name="screens/prepaidCards"
              options={{
                title: i18n.t('prepaidCards.title'),
                headerTintColor: theme.textSecondary,
              }}
            />
          </Stack>
        </CategoryProvider>
      </TransactionProvider>
    </SettingsProvider>
  );
}

export default function RootLayout() {
  useEffect(() => {
    const init = async () => {
      try {
        // 初始化崩溃监控
        await crashReporter.initialize();
        crashReporter.logUserAction('App started');

        // 初始化数据库
        initDatabase();
        crashReporter.addBreadcrumb('Database initialized', 'info', 'system');
      } catch (error) {
        console.error('Failed to initialize app:', error);
        crashReporter.reportError(error instanceof Error ? error : new Error('App initialization failed'));
      }
    };
    init();
  }, []);

  return (
    <ErrorBoundary
      onError={(error, errorInfo) => {
        crashReporter.reportError(error, { errorInfo, location: 'RootLayout' });
      }}
    >
      <GestureHandlerRootView style={{ flex: 1 }}>
        <ThemeProvider>
          <AppStack />
        </ThemeProvider>
      </GestureHandlerRootView>
    </ErrorBoundary>
  );
}
